{% extends 'base.html' %} {% load humanize %}
<!--  -->
{% block title %}{{ budget.term.term_name }} Budget Details | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Breadcrumb -->
  <div class="card-modern p-6 breadcrumb-animation">
    <div class="flex items-center gap-4 text-sm text-[#40657F]">
      <a href="{% url 'finances:budget_ledger_management' %}" class="hover:text-[#7AB2D3] transition-colors duration-200">
        <i class="fas fa-chart-line mr-2"></i>Budget & Ledger Management
      </a>
      <i class="fas fa-chevron-right text-[#B9D8EB]"></i>
      <span class="text-[#2C3E50] font-medium">{{ budget.term.term_name }} Budget</span>
    </div>
  </div>

  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-6">
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-calculator text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            {{ budget.term.term_name }} Budget
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            {{ budget.term.academic_year.name }} • {{ budget.term.start_date }} - {{ budget.term.end_date }}
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#74C69D] to-[#5fb085] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-4 action-buttons-slide-in">
        <a
          href="{% url 'finances:edit_budget' budget.pk %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-4 px-8 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-edit group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Edit Budget</span>
        </a>
        <a
          href="{% url 'finances:budget_ledger_management' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group border border-[#B9D8EB]"
        >
          <i
            class="fas fa-arrow-left group-hover:-translate-x-1 transition-all duration-300"
          ></i>
          <span>Back to Management</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Budget Summary Section -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 stats-cards-fade-in">
    <!-- Total Budget -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#74C69D]/5 to-[#5fb085]/5 border-l-4 border-[#74C69D]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#74C69D] rounded-xl flex items-center justify-center">
          <i class="fas fa-dollar-sign text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Total Budget</h3>
          <p class="text-[#40657F] font-mono font-bold text-2xl">MWK {{ total_budget|intcomma }}</p>
        </div>
      </div>
    </div>

    <!-- Budget Lines Count -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#7AB2D3]/5 to-[#40657F]/5 border-l-4 border-[#7AB2D3]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#7AB2D3] rounded-xl flex items-center justify-center">
          <i class="fas fa-list text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Budget Lines</h3>
          <p class="text-[#40657F] font-bold text-2xl">{{ budget_lines.count }} account{{ budget_lines.count|pluralize }}</p>
        </div>
      </div>
    </div>

    <!-- Academic Term -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#F28C8C]/5 to-[#e74c3c]/5 border-l-4 border-[#F28C8C]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#F28C8C] rounded-xl flex items-center justify-center">
          <i class="fas fa-calendar text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Academic Term</h3>
          <p class="text-[#40657F] font-bold text-lg">{{ budget.term.term_name }}</p>
          <p class="text-[#40657F] text-sm">{{ budget.term.academic_year.name }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Budget Description -->
  {% if budget.description %}
  <div class="card-modern p-6 description-fade-in">
    <div class="flex items-center gap-4 mb-4">
      <div class="w-10 h-10 bg-[#40657F] rounded-lg flex items-center justify-center">
        <i class="fas fa-info-circle text-white"></i>
      </div>
      <h3 class="text-xl font-bold text-[#2C3E50]">Budget Description</h3>
    </div>
    <p class="text-[#40657F] leading-relaxed">{{ budget.description }}</p>
  </div>
  {% endif %}

  <!-- Budget Lines Management Section -->
  <div class="card-modern p-8 budget-lines-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg table-icon-float"
      >
        <i class="fas fa-list-alt text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Budget Lines
        </h3>
        <p class="text-[#40657F] text-sm">
          Manage budget allocations for different accounts
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <!-- Budget Lines Form -->
    <form method="POST" class="space-y-6">
      {% csrf_token %}
      {{ formset.management_form }}
      
      <!-- Budget Lines Table -->
      <div
        class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg table-fade-in"
      >
        <table class="min-w-full bg-white">
          <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
            <tr>
              <th
                class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center gap-2">
                  <i class="fas fa-book text-[#7AB2D3]"></i>
                  Account
                </div>
              </th>
              <th
                class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center gap-2">
                  <i class="fas fa-layer-group text-[#40657F]"></i>
                  Type
                </div>
              </th>
              <th
                class="px-6 py-4 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center justify-end gap-2">
                  <i class="fas fa-dollar-sign text-[#74C69D]"></i>
                  Budget Amount
                </div>
              </th>
              <th
                class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center justify-center gap-2">
                  <i class="fas fa-cog text-[#F28C8C]"></i>
                  Actions
                </div>
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#B9D8EB]/30">
            {% for form in formset %}
            <tr class="budget-line-row hover:bg-[#E2F1F9]/50 transition-colors duration-200">
              {% for hidden in form.hidden_fields %}
                {{ hidden }}
              {% endfor %}
              
              <td class="px-6 py-4">
                {% if form.instance.pk %}
                  <span class="font-bold text-[#2C3E50]">{{ form.instance.account.name }}</span>
                  <span class="block text-sm text-[#40657F] font-mono">{{ form.instance.account.code }}</span>
                  {{ form.account.as_hidden }}
                {% else %}
                  {{ form.account }}
                {% endif %}
              </td>
              
              <td class="px-6 py-4">
                {% if form.instance.pk %}
                  <span
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold
                    {% if form.instance.account.ledger_type == 'Asset' %}bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30
                    {% elif form.instance.account.ledger_type == 'Revenue' %}bg-[#7AB2D3]/20 text-[#7AB2D3] border border-[#7AB2D3]/30
                    {% elif form.instance.account.ledger_type == 'Expense' %}bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30
                    {% else %}bg-[#40657F]/20 text-[#40657F] border border-[#40657F]/30{% endif %}"
                  >
                    {{ form.instance.account.ledger_type }}
                  </span>
                {% else %}
                  <span class="text-[#B9D8EB]">Select account first</span>
                {% endif %}
              </td>
              
              <td class="px-6 py-4">
                {{ form.amount }}
              </td>
              
              <td class="px-6 py-4 text-center">
                {% if form.instance.pk %}
                  <label class="inline-flex items-center gap-2 text-[#F28C8C] hover:text-[#e74c3c] cursor-pointer">
                    {{ form.DELETE }}
                    <i class="fas fa-trash text-sm"></i>
                    <span class="text-sm">Delete</span>
                  </label>
                {% else %}
                  <span class="text-[#B9D8EB] text-sm">New line</span>
                {% endif %}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <!-- Form Actions -->
      <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-[#B9D8EB]/30">
        <button
          type="submit"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-save group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Save Budget Lines</span>
        </button>
        <button
          type="button"
          onclick="window.location.reload()"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group border border-[#B9D8EB]"
        >
          <i
            class="fas fa-undo group-hover:-rotate-45 transition-all duration-300"
          ></i>
          <span>Reset Changes</span>
        </button>
      </div>
    </form>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .breadcrumb-animation {
    opacity: 0;
    transform: translateY(-20px);
    animation: breadcrumbSlideDown 0.6s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  /* Stats Cards Animation */
  .stats-cards-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: statsCardsFadeIn 0.8s ease-out 1s forwards;
  }

  /* Description Animation */
  .description-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: descriptionFadeIn 0.8s ease-out 1.1s forwards;
  }

  /* Budget Lines Section Animation */
  .budget-lines-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: budgetLinesSectionFadeIn 0.8s ease-out 1.2s forwards;
  }

  .table-icon-float {
    animation: tableIconFloat 4s ease-in-out infinite;
  }

  .table-fade-in {
    opacity: 0;
    animation: tableFadeIn 0.8s ease-out 1.4s forwards;
  }

  .budget-line-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: budgetLineRowSlideIn 0.4s ease-out forwards;
  }

  .budget-line-row:nth-child(1) { animation-delay: 1.6s; }
  .budget-line-row:nth-child(2) { animation-delay: 1.7s; }
  .budget-line-row:nth-child(3) { animation-delay: 1.8s; }
  .budget-line-row:nth-child(4) { animation-delay: 1.9s; }
  .budget-line-row:nth-child(5) { animation-delay: 2s; }

  /* Keyframe Definitions */
  @keyframes breadcrumbSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes actionButtonsSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes statsCardsFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes descriptionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes budgetLinesSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes tableIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-3deg); }
  }

  @keyframes tableFadeIn {
    to { opacity: 1; }
  }

  @keyframes budgetLineRowSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .budget-line-row {
      animation-delay: 1.2s;
    }

    .budget-line-row:nth-child(n) {
      animation-delay: calc(1.2s + 0.1s * var(--item-index, 1));
    }
  }
</style>

{% endblock %}
