from django.shortcuts import redirect, render, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Q
from django.core.paginator import Paginator

from finances.forms.outflow_forms import OutFlowLineFormSet, OutflowForm, ReversalReasonForm
from students.utils.fee_calculations import re_work_on_expenditurers

from finances.book_keeping import JournalLine, Outflow, JournalEntry
from finances.fee_management.models.receipt import Receipt
from core.services import journal_reversals


@login_required(login_url="accounts:login")
def expenditure_list(request):
    from datetime import datetime

    total_collected = JournalLine.objects.filter(
        journal_entry__term__is_active=True, account__ledger_type="Revenue", journal_entry__is_reversal=False).aggregate(total=Sum('amount'))['total'] or 0
    total_used = JournalLine.objects.filter(
        journal_entry__term__is_active=True,
        line_type="Debit",
        journal_entry__is_reversal=False,
        journal_entry__is_locked=False,
        account__ledger_type="Expense"  # Only include expense accounts
    ).aggregate(total=Sum('amount'))['total'] or 0
    total_balance = total_collected - total_used

    # Get search and filter parameters
    search_query = request.GET.get('search', '').strip()
    date_from = request.GET.get('date_from', '').strip()
    date_to = request.GET.get('date_to', '').strip()
    payee_filter = request.GET.get('payee', '').strip()
    amount_min = request.GET.get('amount_min', '').strip()
    amount_max = request.GET.get('amount_max', '').strip()

    # Base queryset
    expenditure_list = Outflow.objects.filter(
        term__is_active=True, is_reversed=False
    ).select_related('term', 'created_by').prefetch_related('outflow_lines')

    # Apply search filters
    if search_query:
        expenditure_list = expenditure_list.filter(
            Q(outflow_id__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(payee__icontains=search_query)
        )

    # Apply date filters
    if date_from:
        try:
            date_from_parsed = datetime.strptime(date_from, '%Y-%m-%d').date()
            expenditure_list = expenditure_list.filter(date__gte=date_from_parsed)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_parsed = datetime.strptime(date_to, '%Y-%m-%d').date()
            expenditure_list = expenditure_list.filter(date__lte=date_to_parsed)
        except ValueError:
            pass

    # Apply payee filter
    if payee_filter:
        expenditure_list = expenditure_list.filter(payee__icontains=payee_filter)

    # Apply amount filters
    if amount_min:
        try:
            amount_min_val = float(amount_min)
            # Filter by total_amount property - we'll need to do this in Python since it's a property
            expenditure_list = [exp for exp in expenditure_list if exp.total_amount >= amount_min_val]
        except ValueError:
            pass

    if amount_max:
        try:
            amount_max_val = float(amount_max)
            # Filter by total_amount property - we'll need to do this in Python since it's a property
            if isinstance(expenditure_list, list):
                expenditure_list = [exp for exp in expenditure_list if exp.total_amount <= amount_max_val]
            else:
                expenditure_list = [exp for exp in expenditure_list if exp.total_amount <= amount_max_val]
        except ValueError:
            pass

    # Convert back to queryset if we filtered by amount (which converts to list)
    if isinstance(expenditure_list, list):
        # Get the IDs and create a new queryset to maintain Django functionality
        if expenditure_list:
            ids = [exp.id for exp in expenditure_list]
            expenditure_list = Outflow.objects.filter(id__in=ids, term__is_active=True, is_reversed=False).order_by('-date')
        else:
            expenditure_list = Outflow.objects.none()
    else:
        # Order results
        expenditure_list = expenditure_list.order_by('-date')

    # Add pagination
    paginator = Paginator(expenditure_list, 15)  # Show 15 expenditures per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get unique payees for filter dropdown
    unique_payees = Outflow.objects.filter(
        term__is_active=True, is_reversed=False
    ).values_list('payee', flat=True).distinct().order_by('payee')

    context = {
        'expenditure_list': page_obj,
        'total_collected': total_collected,
        'total_used': total_used,
        'total_balance': total_balance,
        'search_query': search_query,
        'date_from': date_from,
        'date_to': date_to,
        'payee_filter': payee_filter,
        'amount_min': amount_min,
        'amount_max': amount_max,
        'unique_payees': unique_payees,
        'total_count': paginator.count,
        'page_obj': page_obj,
    }
    return render(request, 'finances/expenditures.html', context)


@login_required(login_url="accounts:login")
def expenditure_detail(request, slug):
    try:
        expenditure = Outflow.objects.get(
            slug=slug, term__is_active=True)
    except Outflow.DoesNotExist:
        messages.error(request, "Expenditure not found.")
        return redirect('finances:expenditures')

    context = {
        'expenditure': expenditure,
    }
    return render(request, 'finances/expenditure_detail.html', context)


@login_required(login_url="accounts:login")
def add_expenditure(request):
    from students.models import Term

    term = Term.objects.get_active()
    form = OutflowForm()
    formset = OutFlowLineFormSet()

    if request.method == 'POST':
        form = OutflowForm(request.POST)
        formset = OutFlowLineFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            outflow = form.save(commit=False)
            outflow.created_by = request.user
            outflow.term = term
            outflow.save()

            lines = formset.save(commit=False)
            for line in lines:
                line.outflow = outflow
                line.save()

            outflow.update_journal()

            return redirect('finances:expenditures')

    context = {
        'form': form,
        'formset': formset,
    }

    return render(request, 'finances/add_expenditure.html', context)


@login_required(login_url="accounts:login")
def recalculate_expenditure(request):
    re_work_on_expenditurers()
    return redirect('students:administration')


# Reversal Views
@login_required(login_url="accounts:login")
def reverse_outflow(request, slug):
    """Reverse an outflow transaction"""
    outflow = get_object_or_404(Outflow, slug=slug, is_reversed=False)

    if request.method == 'POST':
        print("POST request received for outflow")
        form = ReversalReasonForm(request.POST)
        print(f"Form data: {request.POST}")
        if form.is_valid():
            print("Form is valid")
            reason = form.cleaned_data['reason']
            print(f"Reason: {reason}")
            try:
                print("About to call reverse_transaction for outflow")
                reversal_entry = journal_reversals.reverse_transaction(
                    outflow, reason, request.user
                )
                print(f"Reversal entry created: {reversal_entry}")
                messages.success(
                    request,
                    f'Outflow {outflow.outflow_id} has been successfully reversed. '
                    f'Reversal entry: {reversal_entry.voucher}'
                )
                return redirect('finances:expenditure_detail', slug=outflow.slug)
            except Exception as e:
                print(f"Exception occurred: {str(e)}")
                messages.error(request, f'Failed to reverse outflow: {str(e)}')
        else:
            print(f"Form is not valid. Errors: {form.errors}")
    else:
        form = ReversalReasonForm()

    context = {
        'form': form,
        'outflow': outflow,
        'title': f'Reverse Outflow {outflow.outflow_id}',
    }
    return render(request, 'finances/reverse_outflow.html', context)


@login_required(login_url="accounts:login")
def reverse_receipt(request, slug):
    """Reverse a receipt transaction"""
    receipt = get_object_or_404(Receipt, slug=slug, is_reversed=False)

    if request.method == 'POST':
        print("POST request received")
        form = ReversalReasonForm(request.POST)
        print(f"Form data: {request.POST}")
        if form.is_valid():
            print("Form is valid")
            reason = form.cleaned_data['reason']
            print(f"Reason: {reason}")
            try:
                print("About to call reverse_transaction")
                reversal_entry = journal_reversals.reverse_transaction(
                    receipt, reason, request.user
                )
                print(f"Reversal entry created: {reversal_entry}")
                messages.success(
                    request,
                    f'Receipt {receipt.receipt_number} has been successfully reversed. '
                    f'Reversal entry: {reversal_entry.voucher}'
                )
                return redirect('students:student_details', slug=receipt.student.student_id)
            except Exception as e:
                print(f"Exception occurred: {str(e)}")
                messages.error(request, f'Failed to reverse receipt: {str(e)}')
        else:
            print(f"Form is not valid. Errors: {form.errors}")
    else:
        form = ReversalReasonForm()

    context = {
        'form': form,
        'receipt': receipt,
        'title': f'Reverse Receipt {receipt.receipt_number}',
    }
    return render(request, 'finances/reverse_receipt.html', context)


@login_required(login_url="accounts:login")
def reversal_list(request):
    """List all reversed transactions"""
    search_query = request.GET.get('search', '')

    # Get all reversal journal entries
    reversals = JournalEntry.objects.filter(is_reversal=True).order_by('-date')

    if search_query:
        reversals = reversals.filter(
            Q(voucher__icontains=search_query) |
            Q(reason__icontains=search_query) |
            Q(created_by__username__icontains=search_query)
        )

    paginator = Paginator(reversals, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_count': reversals.count(),
    }
    return render(request, 'finances/reversal_list.html', context)


@login_required(login_url="accounts:login")
def reversal_detail(request, voucher):
    """View details of a specific reversal"""
    reversal = get_object_or_404(
        JournalEntry, voucher=voucher, is_reversal=True)

    # Get the original transaction that was reversed
    original_transaction = reversal.reverses

    context = {
        'reversal': reversal,
        'original_transaction': original_transaction,
    }
    return render(request, 'finances/reversal_detail.html', context)
